import { createBrowserRouter } from 'react-router-dom';
import { DefaultLayout } from './layouts/DefaultLayout';
import DashboardPage from './pages/DashboardPage';
import UserManagementPage from './pages/UserManagementPage';
import TeamCollaborationPage from './pages/TeamCollaborationPage';
import TaskTablePage from './pages/TaskTablePage';
import AntdTable from './components/AntdTable';
import AlertDrawerDemo from './pages/AlertDrawerDemo';
import AlertTablePage from './pages/AlertTablePage';

export const router = createBrowserRouter([
  {
    path: '/',
    Component: DefaultLayout,
    children: [
      {
        index: true,
        Component: () => <DashboardPage />,
      },
      {
        path: 'dashboard',
        Component: () => <DashboardPage />,
      },
      {
        path: 'users',
        Component: () => <UserManagementPage />,
      },
      {
        path: 'tasks',
        Component: () => <TaskTablePage />,
      },
      {
        path: 'alerts',
        Component: () => <AlertTablePage />,
      },
      {
        path: 'teams',
        Component: () => <TeamCollaborationPage />,
      },
      {
        path: 'alert-drawer-demo',
        Component: () => <AlertDrawerDemo />,
      },
    ],
  },
  {
    path: '*',
    Component: () => <div>404 Not Found</div>,
  },
  {
    path: '/antd/table',
    Component: () => <AntdTable />,
  },
]);
